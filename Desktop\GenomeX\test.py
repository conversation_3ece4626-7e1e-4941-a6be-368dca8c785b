import math
import csv
import os
import PIL


def add_numbers(a, b):
    import math
    import csv
    import os
    import PIL
    """Add two numbers and return the result."""
    return a + b


def subtract_numbers(a, b):
    import math
    import csv
    import os
    import PIL
    """Subtract b from a and return the result."""
    return a - b


def multiply_numbers(a, b):
    import math
    import csv
    import os
    import PIL
    import math
    """Multiply two numbers and return the result."""
    return a * b


def divide_numbers(a, b):
    import math
    import csv
    import os
    import PIL
    import math
    """Divide a by b and return the result."""
    if b == 0:
        return 'Cannot divide by zero'
    return a / b


def check_even(number):
    import math
    """Check if a number is even."""
    return number % 2 == 0


def check_odd(number):
    """Check if a number is odd."""
    return number % 2 != 0


def is_prime(n):
    import math
    """Check if a number is prime."""
    if n <= 1:
        return False
    if n <= 3:
        return True
    if n % 2 == 0 or n % 3 == 0:
        return False
    i = 5
    while i * i <= n:
        if n % i == 0 or n % (i + 2) == 0:
            return False
        i += 6
    return True


def factorial(n):
    import math
    """Calculate factorial of n."""
    if n == 0 or n == 1:
        return 1
    else:
        result = 1
        for i in range(2, n + 1):
            result *= i
        return result


def fibonacci(n):
    import math
    """Return the nth Fibonacci number."""
    if n <= 0:
        return 'Input must be positive integer'
    elif n == 1:
        return 0
    elif n == 2:
        return 1
    else:
        return fibonacci_iterative(n)


def fibonacci_iterative(n):
    import math
    """Return the nth Fibonacci number using iteration."""
    if n <= 0:
        return 'Input must be positive integer'
    if n == 1:
        return 0
    if n == 2:
        return 1
    a, b = 0, 1
    for _ in range(3, n + 1):
        a, b = b, a + b
    return b


def reverse_string(text):
    import math
    """Reverse a string."""
    return text[::-1]


def count_vowels(text):
    import math
    """Count the number of vowels in a string."""
    vowels = set('aeiouAEIOU')
    return sum(1 for char in text if char in vowels)


def count_consonants(text):
    """Count the number of consonants in a string."""
    consonants = set('bcdfghjklmnpqrstvwxyzBCDFGHJKLMNPQRSTVWXYZ')
    return sum(1 for char in text if char in consonants)


def is_palindrome(text):
    """Check if a string is a palindrome."""
    cleaned_text = ''.join(c.lower() for c in text if c.isalnum())
    return cleaned_text == cleaned_text[::-1]


def convert_to_uppercase(text):
    """Convert a string to uppercase."""
    return text.upper()


def convert_to_lowercase(text):
    """Convert a string to lowercase."""
    return text.lower()


def capitalize_first_letter(text):
    """Capitalize the first letter of a string."""
    if not text:
        return text
    return text[0].upper() + text[1:]


def count_words(text):
    """Count the number of words in a string."""
    if not text:
        return 0
    return len(text.split())


def swap_case(text):
    """Swap the case of each character in a string."""
    return text.swapcase()


def calculate_area_rectangle(length, width):
    """Calculate the area of a rectangle."""
    return length * width


def calculate_perimeter_rectangle(length, width):
    """Calculate the perimeter of a rectangle."""
    return 2 * (length + width)


def calculate_area_circle(radius):
    """Calculate the area of a circle."""
    return math.pi * radius ** 2


def calculate_circumference(radius):
    """Calculate the circumference of a circle."""
    return 2 * math.pi * radius


def calculate_area_triangle(base, height):
    """Calculate the area of a triangle."""
    return 0.5 * base * height


def calculate_hypotenuse(a, b):
    """Calculate the hypotenuse of a right triangle."""
    return (a ** 2 + b ** 2) ** 0.5


def calculate_bmi(weight_kg, height_m):
    """Calculate BMI (Body Mass Index)."""
    return weight_kg / height_m ** 2


def celsius_to_fahrenheit(celsius):
    """Convert Celsius to Fahrenheit."""
    return celsius * 9 / 5 + 32


def fahrenheit_to_celsius(fahrenheit):
    """Convert Fahrenheit to Celsius."""
    return (fahrenheit - 32) * 5 / 9


def kilometers_to_miles(kilometers):
    """Convert kilometers to miles."""
    return kilometers * 0.621371


def miles_to_kilometers(miles):
    """Convert miles to kilometers."""
    return miles * 1.60934


def liters_to_gallons(liters):
    """Convert liters to gallons."""
    return liters * 0.264172


def gallons_to_liters(gallons):
    """Convert gallons to liters."""
    return gallons * 3.78541


def kilograms_to_pounds(kilograms):
    """Convert kilograms to pounds."""
    return kilograms * 2.20462


def pounds_to_kilograms(pounds):
    """Convert pounds to kilograms."""
    return pounds * 0.453592


def meters_to_feet(meters):
    """Convert meters to feet."""
    return meters * 3.28084


def feet_to_meters(feet):
    """Convert feet to meters."""
    return feet * 0.3048


def celsius_to_kelvin(celsius):
    """Convert Celsius to Kelvin."""
    return celsius + 273.15


def kelvin_to_celsius(kelvin):
    """Convert Kelvin to Celsius."""
    return kelvin - 273.15


def fahrenheit_to_kelvin(fahrenheit):
    """Convert Fahrenheit to Kelvin."""
    celsius = fahrenheit_to_celsius(fahrenheit)
    return celsius_to_kelvin(celsius)


def kelvin_to_fahrenheit(kelvin):
    """Convert Kelvin to Fahrenheit."""
    celsius = kelvin_to_celsius(kelvin)
    return celsius_to_fahrenheit(celsius)


def average(numbers):
    """Calculate the average of a list of numbers."""
    if not numbers:
        return 0
    return sum(numbers) / len(numbers)


def median(numbers):
    """Calculate the median of a list of numbers."""
    if not numbers:
        return 0
    sorted_numbers = sorted(numbers)
    length = len(sorted_numbers)
    if length % 2 == 0:
        return (sorted_numbers[length // 2 - 1] + sorted_numbers[length // 2]
            ) / 2
    else:
        return sorted_numbers[length // 2]


def find_mode(numbers):
    """Find the mode of a list of numbers."""
    if not numbers:
        return None
    frequency = {}
    for num in numbers:
        frequency[num] = frequency.get(num, 0) + 1
    max_freq = max(frequency.values())
    modes = [k for k, v in frequency.items() if v == max_freq]
    if len(modes) == len(numbers):
        return None
    return modes


def variance(numbers):
    """Calculate the variance of a list of numbers."""
    if not numbers or len(numbers) < 2:
        return 0
    mean = average(numbers)
    return sum((x - mean) ** 2 for x in numbers) / len(numbers)


def standard_deviation(numbers):
    """Calculate the standard deviation of a list of numbers."""
    return math.sqrt(variance(numbers))


def sum_list(numbers):
    """Calculate the sum of a list of numbers."""
    return sum(numbers)
