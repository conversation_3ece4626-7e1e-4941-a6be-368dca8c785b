#Goal: Automatically evolve a Python script by mutating its AST and selecting better-performing variants.
-Load the original Python script (main.py) and parse it into an AST.
-Generate N mutated versions of the AST using random transformations.
-Convert each mutated AST back to source code.
-Run each mutated version and evaluate its performance based on a custom fitness function (e.g., execution speed or output correctness).
-Select the top-performing mutation(s) and repeat the process for a fixed number of generations or until convergence.
