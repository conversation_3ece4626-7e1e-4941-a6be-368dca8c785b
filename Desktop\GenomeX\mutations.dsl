rule "replace range with enumerate" {
  match: """
for i in range(len(__PH_list)):
    __PH_element = __PH_list[i]
"""
  replace: """
for i, __PH_element in enumerate(__PH_list):
    pass
"""
  when: { loop_has_break }
  weight: 0.6
}

rule "simplify double negative if condition" {
  match: """
if not (not __PH_condition):
    __PH_action
"""
  replace: """
if __PH_condition:
    __PH_action
"""
  when: { }
  weight: 0.4
}
