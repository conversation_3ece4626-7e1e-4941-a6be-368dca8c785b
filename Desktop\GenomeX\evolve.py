#!/usr/bin/env python3
"""
GenomeX Evolution System - Main Entry Point
Runs evolution process with default files or user-specified files.
"""

import os
import sys
import logging
import time
from pathlib import Path
from typing import Optional, Tuple

# Import the evolution engines
try:
    from logic import EvolutionEngine
    LOGIC_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Logic module not available: {e}")
    LOGIC_AVAILABLE = False

try:
    from mutation import evolve_code
    MUTATION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Mutation module not available: {e}")
    MUTATION_AVAILABLE = False

from new_ai import gemini

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('evolution.log')
    ]
)

# Default file configurations
DEFAULT_MAIN_FILE = "main.py"
DEFAULT_GOAL_FILE = "goal.txt"
DEFAULT_GENOME_FILE = "main_genome.bin"

def create_default_main_py():
    """Create a default main.py file if it doesn't exist."""
    if not os.path.exists(DEFAULT_MAIN_FILE):
        default_code = '''#!/usr/bin/env python3
"""
Default Python script for evolution testing.
This script demonstrates basic functionality that can be evolved.
"""

import time
import random

def fibonacci(n):
    """Calculate fibonacci number using basic recursion."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def bubble_sort(arr):
    """Sort array using bubble sort algorithm."""
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

def main():
    """Main function demonstrating various operations."""
    print("=== GenomeX Default Script ===")
    
    # Test fibonacci
    print("\\nFibonacci sequence:")
    for i in range(10):
        print(f"fib({i}) = {fibonacci(i)}")
    
    # Test sorting
    print("\\nSorting test:")
    test_array = [random.randint(1, 100) for _ in range(10)]
    print(f"Original: {test_array}")
    sorted_array = bubble_sort(test_array.copy())
    print(f"Sorted: {sorted_array}")
    
    # Performance test
    print("\\nPerformance test:")
    start_time = time.time()
    result = fibonacci(20)
    end_time = time.time()
    print(f"fibonacci(20) = {result}, took {end_time - start_time:.4f} seconds")
    
    print("\\n=== Script completed successfully ===")

if __name__ == "__main__":
    main()
'''
        
        with open(DEFAULT_MAIN_FILE, 'w', encoding='utf-8') as f:
            f.write(default_code)
        logging.info(f"Created default {DEFAULT_MAIN_FILE}")
        return True
    return False

def create_default_goal_file():
    """Create a default goal.txt file if it doesn't exist."""
    if not os.path.exists(DEFAULT_GOAL_FILE):
        default_goal = '''#Goal: Optimize the Python script for better performance and code quality.
#Workflow:
- Analyze the current code structure and identify performance bottlenecks.
- Optimize algorithms to use more efficient approaches (e.g., memoization, iterative solutions).
- Improve code readability and maintainability through better variable names and structure.
- Add error handling and input validation where appropriate.
- Optimize data structures and memory usage.
- Ensure the optimized code maintains the same functionality while running faster.
'''
        
        with open(DEFAULT_GOAL_FILE, 'w', encoding='utf-8') as f:
            f.write(default_goal)
        logging.info(f"Created default {DEFAULT_GOAL_FILE}")
        return True
    return False

def create_genome_from_main():
    """Create a genome file from main.py using the self_rebuild system."""
    try:
        from self_rebuild import GenomeX
        
        if os.path.exists(DEFAULT_MAIN_FILE) and not os.path.exists(DEFAULT_GENOME_FILE):
            genome_system = GenomeX(DEFAULT_MAIN_FILE, DEFAULT_GENOME_FILE)
            genome_system.create_genome()
            logging.info(f"Created genome file: {DEFAULT_GENOME_FILE}")
            return True
    except Exception as e:
        logging.warning(f"Could not create genome file: {e}")
    return False

def get_evolution_method() -> str:
    """Get the evolution method from user or use default."""
    print("\nAvailable evolution methods:")
    print("1. Logic-based evolution (default) - Uses LLM with goal-based optimization")
    print("2. Mutation-based evolution - Uses DSL rules and AST mutations")
    print("3. User-guided evolution - Interactive project creation")
    
    choice = input("\nSelect evolution method (1-3, or press Enter for default): ").strip()
    
    if choice == "2":
        return "mutation"
    elif choice == "3":
        return "user"
    else:
        return "logic"  # default

def run_logic_evolution(genome_file: str, goal_file: str):
    """Run evolution using the logic-based approach."""
    logging.info("Starting logic-based evolution...")
    
    try:
        evolution_engine = EvolutionEngine()
        evolution_engine.evolve(Path(genome_file), Path(goal_file))
        logging.info("Logic-based evolution completed successfully!")
    except Exception as e:
        logging.error(f"Logic-based evolution failed: {e}")
        raise

def run_mutation_evolution(main_file: str):
    """Run evolution using the mutation-based approach."""
    logging.info("Starting mutation-based evolution...")
    
    try:
        output_file = f"evolved_{main_file}"
        evolve_code(main_file, output_file)
        logging.info(f"Mutation-based evolution completed! Output: {output_file}")
    except Exception as e:
        logging.error(f"Mutation-based evolution failed: {e}")
        raise

def run_user_evolution():
    """Run evolution using the user-guided approach."""
    logging.info("Starting user-guided evolution...")
    
    try:
        import user
        user.main()
        logging.info("User-guided evolution completed!")
    except Exception as e:
        logging.error(f"User-guided evolution failed: {e}")
        raise

def test_ai_generation():
    """Test if AI text generation is working properly."""
    print("\n=== Testing AI Generation ===")
    
    try:
        test_prompt = "Generate a simple Python function that adds two numbers. Return only the code."
        logging.info("Testing AI generation with simple prompt...")
        
        # Test basic generation
        response = gemini(test_prompt)
        
        if response and len(response.strip()) > 0:
            print(f"✓ AI generation working! Response length: {len(response)} characters")
            print(f"Sample response: {response[:100]}...")
            return True
        else:
            print("✗ AI generation returned empty response")
            logging.error("AI generation returned empty or None response")
            return False
            
    except Exception as e:
        print(f"✗ AI generation failed with error: {e}")
        logging.error(f"AI generation test failed: {e}")
        return False

def setup_default_files():
    """Set up all default files needed for evolution."""
    print("\n=== Setting up default files ===")
    
    files_created = []
    
    # Create main.py
    if create_default_main_py():
        files_created.append(DEFAULT_MAIN_FILE)
    
    # Create goal.txt
    if create_default_goal_file():
        files_created.append(DEFAULT_GOAL_FILE)
    
    # Create genome file
    if create_genome_from_main():
        files_created.append(DEFAULT_GENOME_FILE)
    
    if files_created:
        print(f"✓ Created default files: {', '.join(files_created)}")
    else:
        print("✓ All default files already exist")
    
    return True

def main():
    """Main entry point for the evolution system."""
    print("=" * 60)
    print("🧬 GenomeX Evolution System")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # Test AI generation first
        if not test_ai_generation():
            print("\n⚠️  Warning: AI generation may not be working properly.")
            print("   Check your API key and internet connection.")
            if input("   Continue anyway? (y/n): ").lower() != 'y':
                return
        
        # Setup default files
        setup_default_files()
        
        # Get evolution method
        method = get_evolution_method()
        
        print(f"\n🚀 Starting {method}-based evolution...")
        
        # Run the selected evolution method
        if method == "logic":
            run_logic_evolution(DEFAULT_GENOME_FILE, DEFAULT_GOAL_FILE)
        elif method == "mutation":
            run_mutation_evolution(DEFAULT_MAIN_FILE)
        elif method == "user":
            run_user_evolution()
        
        # Calculate total time
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ Evolution completed successfully!")
        print(f"⏱️  Total time: {duration:.2f} seconds")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Evolution interrupted by user")
        logging.info("Evolution interrupted by user")
    except Exception as e:
        print(f"\n❌ Evolution failed: {e}")
        logging.error(f"Evolution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
