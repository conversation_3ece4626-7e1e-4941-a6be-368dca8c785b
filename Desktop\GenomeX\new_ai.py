import google.generativeai as genai
import requests
import json
import time
import random
from typing import Optional, List, Dict, Any

# Configure Gemini API
API_KEY = "AIzaSyDzVcofu-epzmPwqqGugQeT7Hox5G2Qk1k"
genai.configure(api_key=API_KEY)

# Try different models in order of preference
MODEL_NAMES = [
    "gemini-1.5-flash",
    "gemini-1.5-pro",
    "gemini-pro",
    "models/gemini-pro"
]

def initialize_model():
    """Initialize the best available Gemini model."""
    global model

    for model_name in MODEL_NAMES:
        try:
            print(f"Trying to initialize model: {model_name}")
            test_model = genai.GenerativeModel(model_name)

            # Test the model with a simple prompt
            test_response = test_model.generate_content("Say 'test'")
            if hasattr(test_response, 'text') and test_response.text:
                model = test_model
                print(f"✓ Successfully initialized model: {model_name}")
                return model_name

        except Exception as e:
            print(f"✗ Failed to initialize {model_name}: {e}")
            continue

    # Fallback to the original model
    try:
        model = genai.GenerativeModel("models/gemma-3-27b-it")
        print("Using fallback model: models/gemma-3-27b-it")
        return "models/gemma-3-27b-it"
    except Exception as e:
        print(f"❌ All models failed to initialize: {e}")
        model = None
        return None

# Initialize the model
current_model = initialize_model()

# Web search configuration
SERPER_API_KEY = "YOUR_SERPER_API_KEY"  # Replace with your actual API key
SEARCH_ENABLED = True  # Set to False to disable web search globally

def web_search(query: str, num_results: int = 3) -> List[Dict[str, Any]]:
    """
    Perform a web search using the Serper API.

    Args:
        query: The search query
        num_results: Number of results to return

    Returns:
        List of search results with title, link, and snippet
    """
    if not SEARCH_ENABLED or not SERPER_API_KEY or SERPER_API_KEY == "YOUR_SERPER_API_KEY":
        return []

    try:
        url = "https://google.serper.dev/search"
        payload = json.dumps({
            "q": query,
            "num": num_results
        })
        headers = {
            'X-API-KEY': SERPER_API_KEY,
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            results = []

            # Process organic results
            if "organic" in data:
                for item in data["organic"][:num_results]:
                    results.append({
                        "title": item.get("title", ""),
                        "link": item.get("link", ""),
                        "snippet": item.get("snippet", "")
                    })

            return results
        return []
    except Exception as e:
        print(f"Web search error: {str(e)}")
        return []

def gemini(prompt: str, use_web_search: bool = False, search_query: Optional[str] = None) -> str:
    """
    Generate content using Gemini model with optional web search enhancement.

    Args:
        prompt: The prompt to send to the model
        use_web_search: Whether to enhance the prompt with web search results
        search_query: Custom search query (if None, will use the prompt)

    Returns:
        Generated text response
    """
    if not prompt or not prompt.strip():
        return "Error: Empty prompt provided"

    if not model:
        return "Error: No AI model available. Check API key and internet connection."

    enhanced_prompt = prompt.strip()

    # Add randomization factor to ensure different responses
    timestamp = int(time.time())
    random_seed = random.randint(1000, 9999)

    if use_web_search and SEARCH_ENABLED:
        query = search_query if search_query else prompt[:100]
        search_results = web_search(query)

        if search_results:
            search_context = "\n\nRelevant information from web search:\n"
            for i, result in enumerate(search_results, 1):
                search_context += f"{i}. {result['title']}\n"
                search_context += f"   URL: {result['link']}\n"
                search_context += f"   {result['snippet']}\n\n"

            enhanced_prompt = f"{search_context}\n\n{prompt}"

    # Add a hidden seed to ensure different responses even for identical prompts
    enhanced_prompt += f"\n\n[Internal: Use creativity seed {timestamp}-{random_seed} for unique response]"

    # Retry mechanism with exponential backoff
    max_retries = 3
    base_delay = 1

    for attempt in range(max_retries):
        try:
            print(f"Attempting AI generation (attempt {attempt + 1}/{max_retries})...")

            # Configure generation parameters for better reliability
            generation_config = {
                "temperature": 0.7,
                "top_p": 0.8,
                "top_k": 40,
                "max_output_tokens": 2048,
            }

            response = model.generate_content(
                enhanced_prompt,
                generation_config=generation_config
            )

            # Check if response has text
            if hasattr(response, 'text') and response.text:
                result_text = response.text.strip()
                if result_text:
                    print(f"✓ AI generation successful! Response length: {len(result_text)} characters")
                    return result_text
                else:
                    print("⚠️ AI returned empty text")

            # Check for blocked content
            if hasattr(response, 'prompt_feedback'):
                feedback = response.prompt_feedback
                if hasattr(feedback, 'block_reason'):
                    print(f"⚠️ Content blocked: {feedback.block_reason}")
                    return f"Content blocked by safety filters: {feedback.block_reason}"

            # If we get here, something went wrong
            print(f"⚠️ Attempt {attempt + 1} failed - no valid response text")

        except Exception as e:
            error_msg = str(e)
            print(f"❌ Attempt {attempt + 1} failed with error: {error_msg}")

            # Check for specific error types
            if "quota" in error_msg.lower() or "limit" in error_msg.lower():
                print("⚠️ API quota/rate limit reached")
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)
                    print(f"⏳ Waiting {delay} seconds before retry...")
                    time.sleep(delay)
                continue

            if "api_key" in error_msg.lower() or "authentication" in error_msg.lower():
                return "Error: Invalid API key or authentication failed"

            if attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)
                print(f"⏳ Waiting {delay} seconds before retry...")
                time.sleep(delay)
            else:
                # Last attempt - try with simplified prompt
                try:
                    print("🔄 Final attempt with simplified prompt...")
                    simplified_prompt = prompt[:300] if len(prompt) > 300 else prompt
                    simplified_prompt += f"\n\n[Simplified request - seed {timestamp}]"

                    response = model.generate_content(simplified_prompt)
                    if hasattr(response, 'text') and response.text:
                        result_text = response.text.strip()
                        if result_text:
                            print("✓ Simplified prompt succeeded!")
                            return result_text
                except:
                    pass

                return f"Error: All attempts failed. Last error: {error_msg}"

    return "Error: Maximum retries exceeded"

def test_gemini_connection():
    """Test the Gemini API connection and model functionality."""
    print("\n=== Testing Gemini API Connection ===")

    if not model:
        print("❌ No model initialized")
        return False

    try:
        # Test 1: Simple generation
        print("Test 1: Simple text generation...")
        response = gemini("Generate the word 'hello' and nothing else.")
        if response and "hello" in response.lower():
            print("✓ Simple generation works")
        else:
            print(f"⚠️ Unexpected response: {response}")

        # Test 2: Code generation
        print("\nTest 2: Code generation...")
        code_response = gemini("Write a Python function that returns 42. Return only the code.")
        if code_response and ("def" in code_response or "return" in code_response):
            print("✓ Code generation works")
        else:
            print(f"⚠️ Code generation may have issues: {code_response}")

        # Test 3: Web search (if enabled)
        if SEARCH_ENABLED and SERPER_API_KEY != "YOUR_SERPER_API_KEY":
            print("\nTest 3: Web search integration...")
            search_response = gemini("What is Python programming?", use_web_search=True)
            if search_response and len(search_response) > 50:
                print("✓ Web search integration works")
            else:
                print("⚠️ Web search may have issues")
        else:
            print("\nTest 3: Web search disabled (no API key)")

        print("\n✅ Gemini API tests completed")
        return True

    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False

def get_model_info():
    """Get information about the current model."""
    if model:
        return f"Current model: {current_model}"
    else:
        return "No model initialized"

# Add a simple test when module is imported
if __name__ == "__main__":
    print(get_model_info())
    test_gemini_connection()

