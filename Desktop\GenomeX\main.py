#!/usr/bin/env python3
"""
Default Python script for evolution testing.
This script demonstrates basic functionality that can be evolved.
"""

import time
import random

def fibonacci(n):
    """Calculate fibonacci number using basic recursion."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibon<PERSON><PERSON>(n-2)

def bubble_sort(arr):
    """Sort array using bubble sort algorithm."""
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

def main():
    """Main function demonstrating various operations."""
    print("=== GenomeX Default Script ===")
    
    # Test fibonacci
    print("\nFibonacci sequence:")
    for i in range(10):
        print(f"fib({i}) = {fibonacci(i)}")
    
    # Test sorting
    print("\nSorting test:")
    test_array = [random.randint(1, 100) for _ in range(10)]
    print(f"Original: {test_array}")
    sorted_array = bubble_sort(test_array.copy())
    print(f"Sorted: {sorted_array}")
    
    # Performance test
    print("\nPerformance test:")
    start_time = time.time()
    result = fibonacci(20)
    end_time = time.time()
    print(f"fibonacci(20) = {result}, took {end_time - start_time:.4f} seconds")
    
    print("\n=== Script completed successfully ===")

if __name__ == "__main__":
    main()
