import re
import ast
import pickle
import brotli
import logging
import subprocess
import tempfile
import astor
import json
from pathlib import Path
import reedsolo
import time
from new_ai import gemini

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

class GenomeProcessor:
    def __init__(self):
        
        self.rs = reedsolo.RSCodec(8)
        logging.info("GenomeProcessor initialized with RSCodec(8)")

    def _decompress(self, data: bytes) -> bytes:
        """Decompresses data using Brotli."""
        logging.info(f"Attempting to decompress data of type {type(data)} and length {len(data)}")
        try:
            decompressed_data = brotli.decompress(data)
            logging.info(f"Decompression successful, result length {len(decompressed_data)}")
            return decompressed_data
        except brotli.Error as e:
            logging.error(f"Decompression failed: {e}")
            raise

    def _ast_from_bytes(self, data: bytes) -> ast.Module:
        """Deserializes an AST from bytes using pickle."""
        logging.info(f"Attempting to deserialize AST from data of type {type(data)} and length {len(data)}")
        try:
            tree = pickle.loads(data)
            if not isinstance(tree, ast.Module):
                raise ValueError("Deserialized object is not an AST Module")
            logging.info("AST deserialization successful")
            return tree
        except (pickle.UnpicklingError, AttributeError, ValueError) as e:
            logging.error(f"AST deserialization failed: {e}")
            raise

    def _decode_genome(self, data: bytes) -> tuple[bytes, dict]:
        """Decodes genome data using Reed-Solomon and splits into AST and metadata."""
        logging.info(f"Attempting to decode genome data of type {type(data)} and length {len(data)}")
        try:
            data_bytes = bytes(data)
            logging.info(f"Input data cast to bytes for decoding, length {len(data_bytes)}")

            rs_decode_result = self.rs.decode(data_bytes)
            logging.info(f"reedsolo.decode result type: {type(rs_decode_result)}, length: {len(rs_decode_result) if hasattr(rs_decode_result, '__len__') else 'N/A'}")

            if not isinstance(rs_decode_result, tuple) or len(rs_decode_result) == 0:
                raise IndexError("reedsolo.decode did not return an expected tuple with data.")

            decoded_data_raw = rs_decode_result[0]
            logging.info(f"Decoded data length: {len(decoded_data_raw) if hasattr(decoded_data_raw, '__len__') else 'N/A'}")
            decoded_data = bytes(decoded_data_raw)

            # Parse AST and metadata
            index = 0
            ast_len = int.from_bytes(decoded_data[index:index+4], byteorder="big")
            index += 4
            ast_compressed = decoded_data[index:index+ast_len]
            index += ast_len
            meta_len = int.from_bytes(decoded_data[index:index+4], byteorder="big")
            index += 4
            meta_compressed = decoded_data[index:index+meta_len]

            # Decompress metadata
            metadata_json = self._decompress(meta_compressed)
            try:
                metadata = json.loads(metadata_json.decode("utf-8"))
            except json.JSONDecodeError as e:
                logging.error(f"Failed to decode metadata: {e}")
                raise ValueError(f"Failed to decode metadata: {e}")

            return ast_compressed, metadata
        except (reedsolo.ReedSolomonError, TypeError, IndexError) as e:
            logging.error(f"Genome decoding failed: {e}")
            raise ValueError("Corrupted genome file or decoding issue") from None
        except Exception as e:
            logging.error(f"An unexpected error occurred during genome decoding: {e}")
            raise ValueError("An unexpected error occurred during genome decoding") from e

    def repair_ast(self, tree: ast.AST) -> ast.AST:
        """Repair AST by setting ctx for ALL nodes with a ctx field."""
        store_targets = set()
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    store_targets.add(id(target))
                    if isinstance(target, ast.Tuple):
                        for elt in target.elts:
                            store_targets.add(id(elt))
            elif isinstance(node, ast.AugAssign):
                store_targets.add(id(node.target))
            elif isinstance(node, ast.For):
                store_targets.add(id(node.target))
                if isinstance(node.target, ast.Tuple):
                    for elt in node.target.elts:
                        store_targets.add(id(elt))
            elif isinstance(node, ast.comprehension):
                store_targets.add(id(node.target))

        for node in ast.walk(tree):
            if hasattr(node, 'ctx') and node.ctx is None:
                ctx = ast.Store() if id(node) in store_targets else ast.Load()
                node.ctx = ctx
                node_id = getattr(node, 'id', getattr(node, 'attr', getattr(node, 'name', 'unknown')))
                logging.debug(f"Repaired {type(node).__name__} node: id={node_id}, set ctx={type(ctx).__name__}")

        return tree

    def load_genome(self, genome_path: Path) -> str:
        """Loads, decodes, and decompresses a genome file into Python code."""
        logging.info(f"Attempting to load genome from: {genome_path}")
        try:
            with open(genome_path, "rb") as f:
                raw_data = f.read()
                logging.info(f"Read raw data from file. Type: {type(raw_data)}, length: {len(raw_data)}")

                ast_compressed, metadata = self._decode_genome(raw_data)
                logging.info(f"Decoded genome: AST compressed length={len(ast_compressed)}, metadata={metadata}")

            ast_data = self._decompress(ast_compressed)
            logging.info(f"Decompressed AST data length: {len(ast_data)}")

            tree = self._ast_from_bytes(ast_data)
            logging.info("AST deserialization successful, attempting to repair AST.")

            tree = self.repair_ast(tree)
            logging.info("AST repair completed, attempting to convert AST to source code.")

            try:
                code_string = astor.to_source(tree)
                logging.debug(f"Generated code snippet: {code_string[:500]}...")
            except Exception as e:
                logging.error(f"Exception occurred during astor.to_source: {e}")
                raise ValueError(f"Failed to convert AST to code string due to exception: {e}")

            if code_string is None:
                logging.error("code_string is None after AST conversion.")
                raise ValueError("code_string is None after AST conversion.")

            logging.info(f"Genome loaded successfully. Final code_string type: {type(code_string)}, length: {len(code_string)}")
            return code_string
        except FileNotFoundError:
            logging.error(f"Genome file not found: {genome_path}")
            raise
        except ValueError as e:
            logging.error(f"Genome decoding or processing failed during loading: {e}")
            raise
        except Exception as e:
            logging.error(f"An unexpected error occurred during genome loading: {e}")
            raise

class EvolutionEngine:
    def __init__(self):
        self.genome_processor = GenomeProcessor()
        logging.info("EvolutionEngine initialized")
        self.code_regex = re.compile(r"```(?:python)?\n(.*?)\n```", re.DOTALL)

    def _is_valid_text(self, text: str) -> bool:
        """Check if the text appears to be valid human-readable content."""
        # Check for high proportion of non-printable or control characters
        non_printable = sum(1 for c in text if ord(c) < 32 or ord(c) > 126)
        return non_printable / len(text) < 0.5 if text else False

    def _sanitize_code(self, raw: str | bytes, current_code: str, retry: bool = True) -> str:
        """Sanitizes raw code string from LLM output."""
        logging.info("Attempting to sanitize code...")
        try:
            # Handle bytes or non-string input from gemini
            if isinstance(raw, bytes):
                logging.info("Received bytes from gemini, attempting to decode as UTF-8")
                try:
                    raw = raw.decode("utf-8", errors="replace")
                except UnicodeDecodeError as e:
                    logging.error(f"Failed to decode gemini output as UTF-8: {e}")
                    raise ValueError(f"Invalid encoding in gemini output: {e}")
            elif not isinstance(raw, str):
                logging.warning(f"_sanitize_code received non-string input: {type(raw)}")
                raw = str(raw)

            logging.info("Calling gemini for initial code sanitization.")
            llm_clean = gemini(f"Sanitize this Python code:\n{raw}")
            logging.info("Gemini sanitization complete.")

            # Handle case where gemini returns bytes
            if isinstance(llm_clean, bytes):
                logging.info("Gemini returned bytes, decoding as UTF-8")
                llm_clean = llm_clean.decode("utf-8", errors="replace")

            logging.info(f"Raw gemini output: {llm_clean[:500]}...")
            logging.info("Extracting code block using regex.")
            match = self.code_regex.search(llm_clean)
            clean = match.group(1) if match else llm_clean
            logging.info(f"Code block extracted. Length: {len(clean)}")
            logging.debug(f"Extracted code: {clean[:500]}...")

            logging.info("Validating code with AST parse.")
            try:
                ast.parse(clean)
                logging.info("AST validation successful.")
                # Remove lines starting with #goal or #workflow
                lines = clean.splitlines()
                cleaned_lines = [line for line in lines if not line.strip().startswith(('#goal', '#workflow'))]
                final_code = "\n".join(cleaned_lines)
                logging.info(f"Removed #goal and #workflow lines. Final code length: {len(final_code)}")
                return final_code
            except SyntaxError as e:
                logging.error(f"AST parsing failed: {e}")
                logging.debug(f"Problematic code: {clean[:1000]}...")
                # Retry with a fallback prompt if allowed
                if retry:
                    logging.info("Retrying with fallback prompt due to invalid code.")
                    fallback_prompt = f"Return valid Python code based on this:\n\n{current_code}"
                    return self._sanitize_code(gemini(fallback_prompt), current_code, retry=False)
                raise SyntaxError(f"Invalid Python code after sanitization: {e}")
        except Exception as e:
            logging.error(f"Sanitization failed: {e}")
            raise

    def evolve(self, genome_path: Path, goal_path: Path):
        """Evolves the genome based on a specified goal."""
        logging.info(f"Starting evolution process for genome: {genome_path} with goal: {goal_path}")
        try:
            current_code = self.genome_processor.load_genome(genome_path)
            logging.info("Genome loaded successfully.")

            # Check if goal file appears binary
            if goal_path.suffix.lower() in ('.bin', '.dat'):
                logging.error("Goal file has a binary extension (.bin, .dat), expected a text file.")
                raise ValueError("Goal file appears to be binary; please provide a text file with optimization goals.")

            try:
                goal = goal_path.read_text(encoding="utf-8", errors="replace")
            except UnicodeDecodeError as e:
                logging.error(f"Failed to decode goal file as UTF-8: {e}")
                raise ValueError(f"Goal file contains invalid UTF-8 characters: {e}")

            # Validate goal content
            if not self._is_valid_text(goal):
                logging.error(f"Goal file content is not valid text: {goal[:100]}...")
                raise ValueError("Goal file contains unreadable or binary data; please provide a valid text file.")

            logging.info(f"Goal read: {goal[:100]}...")
            prompt = f"Optimize this code for {goal}:\n\n{current_code}"
            logging.info("Generated LLM prompt.")

            new_code = self._sanitize_code(gemini(prompt), current_code)
            logging.info("Code evolution and sanitization complete.")

            # Start timing the execution
            start_time = time.time()

            # Create temporary file in the current directory
            tmp_filename = f"temp_evolved_code_{int(time.time())}.py"
            tmp_path = Path(".") / tmp_filename
            try:
                with open(tmp_path, "w", encoding="utf-8") as tmp:
                    tmp.write(new_code)
                logging.info(f"New code written to temporary file: {tmp_path}")
            except IOError as e:
                logging.error(f"Failed to write temporary file {tmp_path}: {e}")
                raise RuntimeError(f"Failed to write temporary file: {e}")

            result = subprocess.run(
                ["python", str(tmp_path)],
                timeout=3000,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding="utf-8",
                errors="replace"
            )

            end_time = time.time()
            duration = end_time - start_time

            if result.returncode == 0:
                logging.info(f"Evolution successful. Output: {result.stdout}")
            else:
                error_message = f"Evolution failed with return code {result.returncode}."
                if result.stderr:
                    error_message += f"\nStderr: {result.stderr}"
                if result.stdout:
                     error_message += f"\nStdout: {result.stdout}"
                logging.error(error_message)
                raise RuntimeError(error_message)
        except Exception as e:
            logging.error(f"Evolution failed: {e}")
            raise
        finally:
            # Ensure tmp_path is defined before attempting deletion
            if 'tmp_path' in locals() and tmp_path.exists():
                if result.returncode == 0:
                    # Rename the temporary file
                    genome_stem = Path(genome_path).stem
                    # Remove '_genome' suffix if present and append '.py'
                    original_base_name = genome_stem.replace('_genome', '')
                    new_filename = f"evolved_{original_base_name}.py"
                    new_path = Path(".") / new_filename
                    try:
                        tmp_path.rename(new_path)
                        logging.info(f"Temporary file renamed to: {new_path}")
                    except Exception as e:
                        logging.warning(f"Failed to rename temporary file {tmp_path} to {new_path}: {e}")
                    # Log the time taken after successful renaming
                    if 'duration' in locals():
                         logging.info(f"Evolution process took {duration:.2f} seconds.")
                else:
                    # Delete the temporary file on error
                    try:
                        tmp_path.unlink()
                        logging.info(f"Temporary file {tmp_path} deleted due to error")
                    except Exception as e:
                        logging.warning(f"Failed to delete temporary file {tmp_path}: {e}")
                    # Log the time taken even on error
                    if 'duration' in locals():
                         logging.info(f"Evolution process took {duration:.2f} seconds before failing.")

if __name__ == "__main__":
    # Start timing the entire process from input
    start_time = time.time()
    try:
        genome_path = input("Enter the path to the genome file: ").strip()
        goal_path = input("Enter the path to the goal file: ").strip()

        if not genome_path or not goal_path:
            raise ValueError("Genome and goal file paths must not be empty")

        # Create Path objects
        genome_path = Path(genome_path)
        goal_path = Path(goal_path)

        if not genome_path.exists():
            raise FileNotFoundError(f"Genome file not found: {genome_path}")
        if not goal_path.exists():
            raise FileNotFoundError(f"Goal file not found: {goal_path}")

        evolution_engine = EvolutionEngine()
        evolution_engine.evolve(genome_path, goal_path)

        # Log the total time taken
        end_time = time.time()
        duration = end_time - start_time
        logging.info(f"Total process took {duration:.2f} seconds.")

    except KeyboardInterrupt:
        logging.info("Program terminated by user")
    except Exception as e:
        logging.error(f"Fatal error during evolution: {e}")
        # Log time taken even on error
        end_time = time.time()
        duration = end_time - start_time
        logging.info(f"Process failed after {duration:.2f} seconds.")
        exit(1)
